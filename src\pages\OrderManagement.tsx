import { useState } from "react";
import { Search, Filter, MoreHorizontal, Eye, Edit, Truck, CheckCircle, Package, Clock } from "lucide-react";
import Header from "@/components/Layout/Header";
import Navigation from "@/components/Layout/Navigation";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Checkbox } from "@/components/ui/checkbox";
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";

const mockOrders = [
  {
    id: "ORD-127",
    customer: "ABC Construction",
    customerEmail: "<EMAIL>",
    type: "Standard Red Brick",
    quantity: "25,000 units",
    status: "processing",
    priority: "high",
    orderDate: "2024-01-25",
    dueDate: "2024-01-30",
    value: 6250.00,
    deliveryAddress: "123 Construction Site, Building District, City",
    notes: "Rush order for commercial project"
  },
  {
    id: "ORD-126",
    customer: "BuildRight Corp",
    customerEmail: "<EMAIL>",
    type: "Fire Brick",
    quantity: "5,000 units",
    status: "shipped",
    priority: "medium",
    orderDate: "2024-01-22",
    dueDate: "2024-01-28",
    value: 6250.00,
    deliveryAddress: "456 Industrial Zone, Manufacturing District, City",
    notes: "Special heat-resistant specifications"
  },
  {
    id: "ORD-125",
    customer: "Metro Developers",
    customerEmail: "<EMAIL>",
    type: "Custom Clay Brick",
    quantity: "15,000 units",
    status: "pending",
    priority: "low",
    orderDate: "2024-01-20",
    dueDate: "2024-02-05",
    value: 5250.00,
    deliveryAddress: "789 Residential Project, Suburb Area, City",
    notes: "Custom color matching required"
  },
  {
    id: "ORD-124",
    customer: "Heritage Restoration",
    customerEmail: "<EMAIL>",
    type: "Engineering Brick",
    quantity: "8,000 units",
    status: "delivered",
    priority: "medium",
    orderDate: "2024-01-18",
    dueDate: "2024-01-25",
    value: 3600.00,
    deliveryAddress: "321 Historic Building, Downtown, City",
    notes: "Historical building restoration project"
  }
];

const OrderManagement = () => {
  const [searchTerm, setSearchTerm] = useState("");
  const [statusFilter, setStatusFilter] = useState("all");
  const [priorityFilter, setPriorityFilter] = useState("all");
  const [selectedOrders, setSelectedOrders] = useState<string[]>([]);
  const [selectedOrder, setSelectedOrder] = useState<string | null>(null);

  const getStatusBadge = (status: string) => {
    const statusConfig = {
      pending: { color: "bg-yellow-100 text-yellow-800 border-yellow-200", icon: Clock },
      processing: { color: "bg-blue-100 text-blue-800 border-blue-200", icon: Package },
      shipped: { color: "bg-purple-100 text-purple-800 border-purple-200", icon: Truck },
      delivered: { color: "bg-green-100 text-green-800 border-green-200", icon: CheckCircle }
    };

    const config = statusConfig[status as keyof typeof statusConfig];
    const Icon = config?.icon || Clock;

    return (
      <Badge className={`${config?.color} border`}>
        <div className="flex items-center space-x-1">
          <Icon className="h-3 w-3" />
          <span className="capitalize">{status}</span>
        </div>
      </Badge>
    );
  };

  const getPriorityBadge = (priority: string) => {
    const priorityConfig = {
      low: { color: "bg-gray-100 text-gray-800 border-gray-200" },
      medium: { color: "bg-yellow-100 text-yellow-800 border-yellow-200" },
      high: { color: "bg-red-100 text-red-800 border-red-200" }
    };

    const config = priorityConfig[priority as keyof typeof priorityConfig];
    return (
      <Badge className={`${config?.color} border`}>
        {priority.toUpperCase()}
      </Badge>
    );
  };

  const filteredOrders = mockOrders.filter(order => {
    const matchesSearch = order.id.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         order.customer.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         order.type.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesStatus = statusFilter === "all" || order.status === statusFilter;
    const matchesPriority = priorityFilter === "all" || order.priority === priorityFilter;
    
    return matchesSearch && matchesStatus && matchesPriority;
  });

  const selectedOrderData = selectedOrder ? mockOrders.find(order => order.id === selectedOrder) : null;

  const handleSelectOrder = (orderId: string, checked: boolean) => {
    if (checked) {
      setSelectedOrders([...selectedOrders, orderId]);
    } else {
      setSelectedOrders(selectedOrders.filter(id => id !== orderId));
    }
  };

  const handleSelectAll = (checked: boolean) => {
    if (checked) {
      setSelectedOrders(filteredOrders.map(order => order.id));
    } else {
      setSelectedOrders([]);
    }
  };

  const handleBulkStatusUpdate = (newStatus: string) => {
    alert(`Updated ${selectedOrders.length} orders to ${newStatus} status`);
    setSelectedOrders([]);
  };

  const handleUpdateOrderStatus = (orderId: string, newStatus: string) => {
    alert(`Updated order ${orderId} to ${newStatus} status`);
  };

  return (
    <div className="min-h-screen bg-background">
      <Header />
      <div className="flex">
        <Navigation currentPath="/admin/orders" userRole="admin" />
        <main className="flex-1 p-6 space-y-6">
          <div className="flex flex-col space-y-2">
            <h1 className="text-3xl font-bold tracking-tight">Order Management</h1>
            <p className="text-muted-foreground">
              Manage all customer orders, update statuses, and track deliveries.
            </p>
          </div>

          {/* Filters and Search */}
          <Card>
            <CardContent className="pt-6">
              <div className="flex flex-col space-y-4 md:flex-row md:space-y-0 md:space-x-4">
                <div className="flex-1">
                  <div className="relative">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                    <Input
                      placeholder="Search orders, customers, or products..."
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                      className="pl-10"
                    />
                  </div>
                </div>
                
                <Select value={statusFilter} onValueChange={setStatusFilter}>
                  <SelectTrigger className="w-full md:w-[180px]">
                    <SelectValue placeholder="Order Status" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Statuses</SelectItem>
                    <SelectItem value="pending">Pending</SelectItem>
                    <SelectItem value="processing">Processing</SelectItem>
                    <SelectItem value="shipped">Shipped</SelectItem>
                    <SelectItem value="delivered">Delivered</SelectItem>
                  </SelectContent>
                </Select>

                <Select value={priorityFilter} onValueChange={setPriorityFilter}>
                  <SelectTrigger className="w-full md:w-[180px]">
                    <SelectValue placeholder="Priority" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Priorities</SelectItem>
                    <SelectItem value="low">Low</SelectItem>
                    <SelectItem value="medium">Medium</SelectItem>
                    <SelectItem value="high">High</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              {/* Bulk Actions */}
              {selectedOrders.length > 0 && (
                <div className="mt-4 p-3 bg-accent rounded-lg">
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium">
                      {selectedOrders.length} order(s) selected
                    </span>
                    <div className="flex space-x-2">
                      <Button size="sm" onClick={() => handleBulkStatusUpdate("processing")}>
                        Mark Processing
                      </Button>
                      <Button size="sm" onClick={() => handleBulkStatusUpdate("shipped")}>
                        Mark Shipped
                      </Button>
                      <Button size="sm" onClick={() => handleBulkStatusUpdate("delivered")}>
                        Mark Delivered
                      </Button>
                    </div>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Orders Table */}
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <CardTitle>Orders ({filteredOrders.length})</CardTitle>
                <div className="flex items-center space-x-2">
                  <Checkbox
                    checked={selectedOrders.length === filteredOrders.length && filteredOrders.length > 0}
                    onCheckedChange={handleSelectAll}
                  />
                  <span className="text-sm text-muted-foreground">Select All</span>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {filteredOrders.map((order) => (
                  <div key={order.id} className="flex items-center space-x-4 p-4 rounded-lg border bg-card hover:bg-accent/50 transition-colors">
                    <Checkbox
                      checked={selectedOrders.includes(order.id)}
                      onCheckedChange={(checked) => handleSelectOrder(order.id, checked as boolean)}
                    />
                    
                    <div className="flex-1 grid gap-4 md:grid-cols-6">
                      <div>
                        <p className="font-medium">{order.id}</p>
                        <p className="text-sm text-muted-foreground">{order.customer}</p>
                      </div>
                      
                      <div>
                        <p className="font-medium">{order.type}</p>
                        <p className="text-sm text-muted-foreground">{order.quantity}</p>
                      </div>
                      
                      <div>
                        <p className="font-medium">${order.value.toFixed(2)}</p>
                        <p className="text-sm text-muted-foreground">Order: {order.orderDate}</p>
                      </div>
                      
                      <div>
                        <p className="font-medium">Due: {order.dueDate}</p>
                        <p className="text-sm text-muted-foreground">
                          {Math.ceil((new Date(order.dueDate).getTime() - new Date().getTime()) / (1000 * 60 * 60 * 24))} days
                        </p>
                      </div>
                      
                      <div className="flex flex-col space-y-1">
                        {getStatusBadge(order.status)}
                        {getPriorityBadge(order.priority)}
                      </div>
                      
                      <div className="flex items-center space-x-2">
                        <Dialog>
                          <DialogTrigger asChild>
                            <Button variant="outline" size="sm" onClick={() => setSelectedOrder(order.id)}>
                              <Eye className="h-4 w-4" />
                            </Button>
                          </DialogTrigger>
                          <DialogContent className="max-w-2xl">
                            <DialogHeader>
                              <DialogTitle>Order Details - {order.id}</DialogTitle>
                            </DialogHeader>
                            {selectedOrderData && (
                              <div className="space-y-4">
                                <div className="grid gap-4 md:grid-cols-2">
                                  <div>
                                    <Label className="font-semibold">Customer Information</Label>
                                    <div className="mt-2 space-y-1 text-sm">
                                      <p><strong>Name:</strong> {selectedOrderData.customer}</p>
                                      <p><strong>Email:</strong> {selectedOrderData.customerEmail}</p>
                                    </div>
                                  </div>
                                  <div>
                                    <Label className="font-semibold">Order Details</Label>
                                    <div className="mt-2 space-y-1 text-sm">
                                      <p><strong>Product:</strong> {selectedOrderData.type}</p>
                                      <p><strong>Quantity:</strong> {selectedOrderData.quantity}</p>
                                      <p><strong>Value:</strong> ${selectedOrderData.value.toFixed(2)}</p>
                                    </div>
                                  </div>
                                </div>
                                
                                <div>
                                  <Label className="font-semibold">Delivery Address</Label>
                                  <p className="mt-1 text-sm text-muted-foreground">{selectedOrderData.deliveryAddress}</p>
                                </div>
                                
                                <div>
                                  <Label className="font-semibold">Notes</Label>
                                  <p className="mt-1 text-sm text-muted-foreground">{selectedOrderData.notes}</p>
                                </div>
                                
                                <div>
                                  <Label htmlFor="status-update">Update Status</Label>
                                  <Select onValueChange={(value) => handleUpdateOrderStatus(selectedOrderData.id, value)}>
                                    <SelectTrigger className="mt-1">
                                      <SelectValue placeholder="Select new status" />
                                    </SelectTrigger>
                                    <SelectContent>
                                      <SelectItem value="pending">Pending</SelectItem>
                                      <SelectItem value="processing">Processing</SelectItem>
                                      <SelectItem value="shipped">Shipped</SelectItem>
                                      <SelectItem value="delivered">Delivered</SelectItem>
                                    </SelectContent>
                                  </Select>
                                </div>
                                
                                <div>
                                  <Label htmlFor="admin-notes">Admin Notes</Label>
                                  <Textarea
                                    id="admin-notes"
                                    placeholder="Add internal notes about this order..."
                                    className="mt-1"
                                  />
                                </div>
                              </div>
                            )}
                          </DialogContent>
                        </Dialog>
                        
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button variant="outline" size="sm">
                              <MoreHorizontal className="h-4 w-4" />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end">
                            <DropdownMenuItem onClick={() => handleUpdateOrderStatus(order.id, "processing")}>
                              <Package className="mr-2 h-4 w-4" />
                              Mark Processing
                            </DropdownMenuItem>
                            <DropdownMenuItem onClick={() => handleUpdateOrderStatus(order.id, "shipped")}>
                              <Truck className="mr-2 h-4 w-4" />
                              Mark Shipped
                            </DropdownMenuItem>
                            <DropdownMenuItem onClick={() => handleUpdateOrderStatus(order.id, "delivered")}>
                              <CheckCircle className="mr-2 h-4 w-4" />
                              Mark Delivered
                            </DropdownMenuItem>
                            <DropdownMenuItem>
                              <Edit className="mr-2 h-4 w-4" />
                              Edit Order
                            </DropdownMenuItem>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </div>
                    </div>
                  </div>
                ))}

                {filteredOrders.length === 0 && (
                  <div className="text-center py-12">
                    <Package className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                    <h3 className="font-semibold mb-2">No orders found</h3>
                    <p className="text-muted-foreground">
                      {searchTerm || statusFilter !== "all" || priorityFilter !== "all" 
                        ? "Try adjusting your filters" 
                        : "No orders available"}
                    </p>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </main>
      </div>
    </div>
  );
};

export default OrderManagement;
