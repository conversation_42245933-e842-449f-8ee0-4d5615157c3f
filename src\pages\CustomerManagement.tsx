import { useState } from "react";
import { Search, Users, Eye, Edit, Mail, Phone, MapPin, Calendar, DollarSign, Package } from "lucide-react";
import Header from "@/components/Layout/Header";
import Navigation from "@/components/Layout/Navigation";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Separator } from "@/components/ui/separator";

const mockCustomers = [
  {
    id: "CUST-001",
    name: "ABC Construction",
    email: "<EMAIL>",
    phone: "+****************",
    company: "ABC Construction Ltd.",
    address: "123 Construction Ave, Building District, City, State 12345",
    joinDate: "2023-06-15",
    status: "active",
    totalOrders: 15,
    totalSpent: 45230.00,
    lastOrderDate: "2024-01-25",
    contactPerson: "John Smith",
    jobTitle: "Project Manager",
    notes: "Large commercial construction company, reliable payment history"
  },
  {
    id: "CUST-002",
    name: "BuildRight Corp",
    email: "<EMAIL>",
    phone: "+****************",
    company: "BuildRight Corporation",
    address: "456 Industrial Zone, Manufacturing District, City, State 23456",
    joinDate: "2023-08-22",
    status: "active",
    totalOrders: 8,
    totalSpent: 28750.00,
    lastOrderDate: "2024-01-22",
    contactPerson: "Sarah Johnson",
    jobTitle: "Procurement Manager",
    notes: "Specializes in industrial projects, prefers fire-resistant materials"
  },
  {
    id: "CUST-003",
    name: "Metro Developers",
    email: "<EMAIL>",
    phone: "+****************",
    company: "Metro Developers Inc.",
    address: "789 Residential Project, Suburb Area, City, State 34567",
    joinDate: "2023-11-10",
    status: "active",
    totalOrders: 12,
    totalSpent: 32100.00,
    lastOrderDate: "2024-01-20",
    contactPerson: "Mike Davis",
    jobTitle: "Development Director",
    notes: "Residential development focus, often requires custom brick colors"
  },
  {
    id: "CUST-004",
    name: "Heritage Restoration",
    email: "<EMAIL>",
    phone: "+****************",
    company: "Heritage Restoration Services",
    address: "321 Historic Building, Downtown, City, State 45678",
    joinDate: "2023-04-03",
    status: "inactive",
    totalOrders: 5,
    totalSpent: 15600.00,
    lastOrderDate: "2023-12-15",
    contactPerson: "Emily Wilson",
    jobTitle: "Restoration Specialist",
    notes: "Historical building restoration, requires exact brick matching"
  }
];

const mockCustomerOrders = {
  "CUST-001": [
    { id: "ORD-127", type: "Standard Red Brick", quantity: "25,000", amount: 6250.00, date: "2024-01-25", status: "processing" },
    { id: "ORD-120", type: "Engineering Brick", quantity: "10,000", amount: 4500.00, date: "2024-01-15", status: "delivered" },
    { id: "ORD-115", type: "Standard Red Brick", quantity: "20,000", amount: 5000.00, date: "2024-01-05", status: "delivered" }
  ],
  "CUST-002": [
    { id: "ORD-126", type: "Fire Brick", quantity: "5,000", amount: 6250.00, date: "2024-01-22", status: "shipped" },
    { id: "ORD-118", type: "Fire Brick", quantity: "3,000", amount: 3750.00, date: "2024-01-10", status: "delivered" }
  ],
  "CUST-003": [
    { id: "ORD-125", type: "Custom Clay Brick", quantity: "15,000", amount: 5250.00, date: "2024-01-20", status: "pending" },
    { id: "ORD-119", type: "Custom Clay Brick", quantity: "8,000", amount: 2800.00, date: "2024-01-12", status: "delivered" }
  ]
};

const CustomerManagement = () => {
  const [searchTerm, setSearchTerm] = useState("");
  const [statusFilter, setStatusFilter] = useState("all");
  const [selectedCustomer, setSelectedCustomer] = useState<string | null>(null);

  const getStatusBadge = (status: string) => {
    const statusConfig = {
      active: { color: "bg-green-100 text-green-800 border-green-200" },
      inactive: { color: "bg-gray-100 text-gray-800 border-gray-200" },
      suspended: { color: "bg-red-100 text-red-800 border-red-200" }
    };

    const config = statusConfig[status as keyof typeof statusConfig];
    return (
      <Badge className={`${config?.color} border`}>
        {status.charAt(0).toUpperCase() + status.slice(1)}
      </Badge>
    );
  };

  const filteredCustomers = mockCustomers.filter(customer => {
    const matchesSearch = customer.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         customer.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         customer.contactPerson.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesStatus = statusFilter === "all" || customer.status === statusFilter;
    
    return matchesSearch && matchesStatus;
  });

  const selectedCustomerData = selectedCustomer ? mockCustomers.find(customer => customer.id === selectedCustomer) : null;
  const customerOrders = selectedCustomer ? mockCustomerOrders[selectedCustomer as keyof typeof mockCustomerOrders] || [] : [];

  const totalCustomers = mockCustomers.length;
  const activeCustomers = mockCustomers.filter(c => c.status === "active").length;
  const totalRevenue = mockCustomers.reduce((sum, customer) => sum + customer.totalSpent, 0);
  const avgOrderValue = totalRevenue / mockCustomers.reduce((sum, customer) => sum + customer.totalOrders, 0);

  return (
    <div className="min-h-screen bg-background">
      <Header />
      <div className="flex">
        <Navigation currentPath="/admin/customers" userRole="admin" />
        <main className="flex-1 p-6 space-y-6">
          <div className="flex flex-col space-y-2">
            <h1 className="text-3xl font-bold tracking-tight">Customer Management</h1>
            <p className="text-muted-foreground">
              Manage customer accounts, view order history, and track customer relationships.
            </p>
          </div>

          {/* Summary Cards */}
          <div className="grid gap-6 md:grid-cols-4">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium text-muted-foreground">Total Customers</CardTitle>
                <Users className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{totalCustomers}</div>
                <p className="text-xs text-muted-foreground mt-1">All time</p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium text-muted-foreground">Active Customers</CardTitle>
                <Users className="h-4 w-4 text-green-600" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{activeCustomers}</div>
                <p className="text-xs text-muted-foreground mt-1">{Math.round((activeCustomers/totalCustomers)*100)}% of total</p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium text-muted-foreground">Total Revenue</CardTitle>
                <DollarSign className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">${totalRevenue.toFixed(2)}</div>
                <p className="text-xs text-muted-foreground mt-1">From all customers</p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium text-muted-foreground">Avg Order Value</CardTitle>
                <Package className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">${avgOrderValue.toFixed(2)}</div>
                <p className="text-xs text-muted-foreground mt-1">Per order</p>
              </CardContent>
            </Card>
          </div>

          {/* Filters and Search */}
          <Card>
            <CardContent className="pt-6">
              <div className="flex flex-col space-y-4 md:flex-row md:space-y-0 md:space-x-4">
                <div className="flex-1">
                  <div className="relative">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                    <Input
                      placeholder="Search customers by name, email, or contact person..."
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                      className="pl-10"
                    />
                  </div>
                </div>
                
                <Select value={statusFilter} onValueChange={setStatusFilter}>
                  <SelectTrigger className="w-full md:w-[180px]">
                    <SelectValue placeholder="Customer Status" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Statuses</SelectItem>
                    <SelectItem value="active">Active</SelectItem>
                    <SelectItem value="inactive">Inactive</SelectItem>
                    <SelectItem value="suspended">Suspended</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </CardContent>
          </Card>

          {/* Customers List */}
          <Card>
            <CardHeader>
              <CardTitle>Customers ({filteredCustomers.length})</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {filteredCustomers.map((customer) => (
                  <div key={customer.id} className="flex items-center space-x-4 p-4 rounded-lg border bg-card hover:bg-accent/50 transition-colors">
                    <Avatar className="h-12 w-12">
                      <AvatarImage src={`/placeholder-avatar-${customer.id}.jpg`} />
                      <AvatarFallback>
                        {customer.name.split(' ').map(n => n[0]).join('').substring(0, 2)}
                      </AvatarFallback>
                    </Avatar>
                    
                    <div className="flex-1 grid gap-4 md:grid-cols-5">
                      <div>
                        <p className="font-medium">{customer.name}</p>
                        <p className="text-sm text-muted-foreground">{customer.contactPerson}</p>
                      </div>
                      
                      <div>
                        <p className="font-medium">{customer.email}</p>
                        <p className="text-sm text-muted-foreground">{customer.phone}</p>
                      </div>
                      
                      <div>
                        <p className="font-medium">{customer.totalOrders} orders</p>
                        <p className="text-sm text-muted-foreground">Last: {customer.lastOrderDate}</p>
                      </div>
                      
                      <div>
                        <p className="font-medium">${customer.totalSpent.toFixed(2)}</p>
                        <p className="text-sm text-muted-foreground">Total spent</p>
                      </div>
                      
                      <div className="flex items-center justify-between">
                        {getStatusBadge(customer.status)}
                        <Dialog>
                          <DialogTrigger asChild>
                            <Button variant="outline" size="sm" onClick={() => setSelectedCustomer(customer.id)}>
                              <Eye className="h-4 w-4" />
                            </Button>
                          </DialogTrigger>
                          <DialogContent className="max-w-4xl">
                            <DialogHeader>
                              <DialogTitle>Customer Details - {customer.name}</DialogTitle>
                            </DialogHeader>
                            {selectedCustomerData && (
                              <Tabs defaultValue="details" className="space-y-4">
                                <TabsList>
                                  <TabsTrigger value="details">Details</TabsTrigger>
                                  <TabsTrigger value="orders">Order History</TabsTrigger>
                                  <TabsTrigger value="notes">Notes</TabsTrigger>
                                </TabsList>
                                
                                <TabsContent value="details" className="space-y-4">
                                  <div className="grid gap-6 md:grid-cols-2">
                                    <div>
                                      <h4 className="font-semibold mb-3">Contact Information</h4>
                                      <div className="space-y-2 text-sm">
                                        <div className="flex items-center space-x-2">
                                          <Mail className="h-4 w-4 text-muted-foreground" />
                                          <span>{selectedCustomerData.email}</span>
                                        </div>
                                        <div className="flex items-center space-x-2">
                                          <Phone className="h-4 w-4 text-muted-foreground" />
                                          <span>{selectedCustomerData.phone}</span>
                                        </div>
                                        <div className="flex items-start space-x-2">
                                          <MapPin className="h-4 w-4 text-muted-foreground mt-0.5" />
                                          <span>{selectedCustomerData.address}</span>
                                        </div>
                                      </div>
                                    </div>
                                    
                                    <div>
                                      <h4 className="font-semibold mb-3">Account Information</h4>
                                      <div className="space-y-2 text-sm">
                                        <div className="flex justify-between">
                                          <span>Customer ID:</span>
                                          <span className="font-medium">{selectedCustomerData.id}</span>
                                        </div>
                                        <div className="flex justify-between">
                                          <span>Join Date:</span>
                                          <span className="font-medium">{selectedCustomerData.joinDate}</span>
                                        </div>
                                        <div className="flex justify-between">
                                          <span>Status:</span>
                                          {getStatusBadge(selectedCustomerData.status)}
                                        </div>
                                        <div className="flex justify-between">
                                          <span>Total Orders:</span>
                                          <span className="font-medium">{selectedCustomerData.totalOrders}</span>
                                        </div>
                                        <div className="flex justify-between">
                                          <span>Total Spent:</span>
                                          <span className="font-medium">${selectedCustomerData.totalSpent.toFixed(2)}</span>
                                        </div>
                                      </div>
                                    </div>
                                  </div>
                                </TabsContent>
                                
                                <TabsContent value="orders" className="space-y-4">
                                  <h4 className="font-semibold">Recent Orders</h4>
                                  <div className="space-y-3">
                                    {customerOrders.map((order) => (
                                      <div key={order.id} className="flex items-center justify-between p-3 rounded-lg border">
                                        <div>
                                          <p className="font-medium">{order.id}</p>
                                          <p className="text-sm text-muted-foreground">{order.type} - {order.quantity} units</p>
                                        </div>
                                        <div className="text-right">
                                          <p className="font-medium">${order.amount.toFixed(2)}</p>
                                          <p className="text-sm text-muted-foreground">{order.date}</p>
                                        </div>
                                      </div>
                                    ))}
                                    {customerOrders.length === 0 && (
                                      <p className="text-muted-foreground text-center py-4">No orders found</p>
                                    )}
                                  </div>
                                </TabsContent>
                                
                                <TabsContent value="notes" className="space-y-4">
                                  <div>
                                    <h4 className="font-semibold mb-2">Customer Notes</h4>
                                    <p className="text-sm text-muted-foreground">{selectedCustomerData.notes}</p>
                                  </div>
                                </TabsContent>
                              </Tabs>
                            )}
                          </DialogContent>
                        </Dialog>
                      </div>
                    </div>
                  </div>
                ))}

                {filteredCustomers.length === 0 && (
                  <div className="text-center py-12">
                    <Users className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                    <h3 className="font-semibold mb-2">No customers found</h3>
                    <p className="text-muted-foreground">
                      {searchTerm || statusFilter !== "all" 
                        ? "Try adjusting your filters" 
                        : "No customers available"}
                    </p>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </main>
      </div>
    </div>
  );
};

export default CustomerManagement;
