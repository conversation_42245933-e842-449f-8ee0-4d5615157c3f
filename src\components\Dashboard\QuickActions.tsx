
import { Plus, Search, MessageSquare, CreditCard } from "lucide-react";
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";

const actions = [
  {
    title: "New Order",
    description: "Create a custom brick order",
    icon: Plus,
    variant: "default" as const,
    href: "/order"
  },
  {
    title: "Track Order",
    description: "Check order status",
    icon: Search,
    variant: "outline" as const,
    href: "/tracking"
  },
  {
    title: "Contact Support",
    description: "Get help with your order",
    icon: MessageSquare,
    variant: "outline" as const,
    href: "/support"
  },
  {
    title: "Make Payment",
    description: "Pay pending invoices",
    icon: CreditCard,
    variant: "outline" as const,
    href: "/payments"
  }
];

const QuickActions = () => {
  return (
    <Card>
      <CardHeader>
        <CardTitle>Quick Actions</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="grid gap-3 md:grid-cols-2">
          {actions.map((action) => {
            const Icon = action.icon;
            return (
              <Button
                key={action.title}
                variant={action.variant}
                className="h-auto p-4 flex flex-col items-start space-y-2 hover:shadow-md transition-all"
              >
                <div className="flex items-center space-x-2">
                  <Icon className="h-5 w-5" />
                  <span className="font-medium">{action.title}</span>
                </div>
                <p className="text-xs text-left text-muted-foreground">
                  {action.description}
                </p>
              </Button>
            );
          })}
        </div>
      </CardContent>
    </Card>
  );
};

export default QuickActions;
