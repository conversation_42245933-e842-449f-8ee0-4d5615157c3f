
import { TrendingUp, Package, Clock, CheckCircle } from "lucide-react";
import { <PERSON>, <PERSON><PERSON><PERSON>nt, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card";

const stats = [
  {
    title: "Total Orders",
    value: "24",
    change: "+12%",
    changeType: "positive" as const,
    icon: Package,
    description: "This month"
  },
  {
    title: "Pending Orders",
    value: "3",
    change: "-5%",
    changeType: "positive" as const,
    icon: Clock,
    description: "In production"
  },
  {
    title: "Completed Orders",
    value: "21",
    change: "+18%",
    changeType: "positive" as const,
    icon: CheckCircle,
    description: "This month"
  },
  {
    title: "Total Spent",
    value: "$12,450",
    change: "+22%",
    changeType: "positive" as const,
    icon: TrendingUp,
    description: "This year"
  },
];

const StatsCards = () => {
  return (
    <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
      {stats.map((stat) => {
        const Icon = stat.icon;
        return (
          <Card key={stat.title} className="hover:shadow-md transition-shadow">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-muted-foreground">
                {stat.title}
              </CardTitle>
              <Icon className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stat.value}</div>
              <p className="text-xs text-muted-foreground mt-1">
                <span className="text-emerald-600 font-medium">{stat.change}</span> {stat.description}
              </p>
            </CardContent>
          </Card>
        );
      })}
    </div>
  );
};

export default StatsCards;
