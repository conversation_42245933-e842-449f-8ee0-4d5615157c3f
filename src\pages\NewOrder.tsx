import { useState } from "react";
import { Plus, Minus, Calculator, MapPin, Calendar } from "lucide-react";
import Header from "@/components/Layout/Header";
import Navigation from "@/components/Layout/Navigation";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Textarea } from "@/components/ui/textarea";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";

const brickTypes = [
  {
    id: "standard-red",
    name: "Standard Red Brick",
    price: 0.25,
    description: "Classic red clay brick for general construction",
    dimensions: "215mm x 102.5mm x 65mm",
    weight: "3.1kg"
  },
  {
    id: "custom-clay",
    name: "Custom Clay Brick",
    price: 0.35,
    description: "Custom colored clay brick with various finishes",
    dimensions: "215mm x 102.5mm x 65mm",
    weight: "3.2kg"
  },
  {
    id: "fire-brick",
    name: "Fire Brick",
    price: 1.25,
    description: "Heat-resistant brick for furnaces and fireplaces",
    dimensions: "230mm x 114mm x 76mm",
    weight: "4.2kg"
  },
  {
    id: "engineering-brick",
    name: "Engineering Brick",
    price: 0.45,
    description: "High-strength brick for structural applications",
    dimensions: "215mm x 102.5mm x 65mm",
    weight: "3.5kg"
  }
];

const NewOrder = () => {
  const [selectedBrick, setSelectedBrick] = useState("");
  const [quantity, setQuantity] = useState(1000);
  const [deliveryDate, setDeliveryDate] = useState("");
  const [deliveryAddress, setDeliveryAddress] = useState("");
  const [specialInstructions, setSpecialInstructions] = useState("");

  const selectedBrickData = brickTypes.find(brick => brick.id === selectedBrick);
  const subtotal = selectedBrickData ? selectedBrickData.price * quantity : 0;
  const deliveryFee = subtotal > 1000 ? 0 : 150;
  const tax = subtotal * 0.15;
  const total = subtotal + deliveryFee + tax;

  const handleQuantityChange = (change: number) => {
    const newQuantity = Math.max(100, quantity + change);
    setQuantity(newQuantity);
  };

  const handleSubmitOrder = () => {
    // Mock order submission
    alert("Order submitted successfully! Order ID: ORD-" + Math.random().toString(36).substr(2, 6).toUpperCase());
  };

  return (
    <div className="min-h-screen bg-background">
      <Header />
      <div className="flex">
        <Navigation currentPath="/order" />
        <main className="flex-1 p-6 space-y-6">
          <div className="flex flex-col space-y-2">
            <h1 className="text-3xl font-bold tracking-tight">New Order</h1>
            <p className="text-muted-foreground">
              Create a custom brick order with your specifications.
            </p>
          </div>

          <div className="grid gap-6 lg:grid-cols-3">
            <div className="lg:col-span-2 space-y-6">
              {/* Brick Selection */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center space-x-2">
                    <Plus className="h-5 w-5" />
                    <span>Select Brick Type</span>
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid gap-4 md:grid-cols-2">
                    {brickTypes.map((brick) => (
                      <div
                        key={brick.id}
                        className={`p-4 rounded-lg border cursor-pointer transition-all hover:shadow-md ${
                          selectedBrick === brick.id 
                            ? "border-primary bg-primary/5 ring-2 ring-primary/20" 
                            : "border-border hover:border-primary/50"
                        }`}
                        onClick={() => setSelectedBrick(brick.id)}
                      >
                        <div className="flex justify-between items-start mb-2">
                          <h3 className="font-semibold">{brick.name}</h3>
                          <Badge variant="secondary">${brick.price}/unit</Badge>
                        </div>
                        <p className="text-sm text-muted-foreground mb-2">{brick.description}</p>
                        <div className="text-xs text-muted-foreground space-y-1">
                          <p>Dimensions: {brick.dimensions}</p>
                          <p>Weight: {brick.weight}</p>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>

              {/* Quantity Selection */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center space-x-2">
                    <Calculator className="h-5 w-5" />
                    <span>Quantity</span>
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="flex items-center space-x-4">
                    <Button
                      variant="outline"
                      size="icon"
                      onClick={() => handleQuantityChange(-100)}
                      disabled={quantity <= 100}
                    >
                      <Minus className="h-4 w-4" />
                    </Button>
                    <div className="flex-1">
                      <Input
                        type="number"
                        value={quantity}
                        onChange={(e) => setQuantity(Math.max(100, parseInt(e.target.value) || 100))}
                        className="text-center text-lg font-semibold"
                        min="100"
                        step="100"
                      />
                    </div>
                    <Button
                      variant="outline"
                      size="icon"
                      onClick={() => handleQuantityChange(100)}
                    >
                      <Plus className="h-4 w-4" />
                    </Button>
                  </div>
                  <p className="text-sm text-muted-foreground mt-2 text-center">
                    Minimum order: 100 units
                  </p>
                </CardContent>
              </Card>

              {/* Delivery Details */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center space-x-2">
                    <MapPin className="h-5 w-5" />
                    <span>Delivery Details</span>
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid gap-4 md:grid-cols-2">
                    <div className="space-y-2">
                      <Label htmlFor="delivery-date">Preferred Delivery Date</Label>
                      <Input
                        id="delivery-date"
                        type="date"
                        value={deliveryDate}
                        onChange={(e) => setDeliveryDate(e.target.value)}
                        min={new Date().toISOString().split('T')[0]}
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="delivery-time">Delivery Time</Label>
                      <Select>
                        <SelectTrigger>
                          <SelectValue placeholder="Select time slot" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="morning">Morning (8AM - 12PM)</SelectItem>
                          <SelectItem value="afternoon">Afternoon (12PM - 5PM)</SelectItem>
                          <SelectItem value="anytime">Anytime</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="delivery-address">Delivery Address</Label>
                    <Textarea
                      id="delivery-address"
                      placeholder="Enter complete delivery address..."
                      value={deliveryAddress}
                      onChange={(e) => setDeliveryAddress(e.target.value)}
                      rows={3}
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="special-instructions">Special Instructions</Label>
                    <Textarea
                      id="special-instructions"
                      placeholder="Any special delivery instructions or requirements..."
                      value={specialInstructions}
                      onChange={(e) => setSpecialInstructions(e.target.value)}
                      rows={3}
                    />
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Order Summary */}
            <div>
              <Card className="sticky top-6">
                <CardHeader>
                  <CardTitle>Order Summary</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  {selectedBrickData ? (
                    <>
                      <div className="space-y-2">
                        <div className="flex justify-between">
                          <span className="font-medium">{selectedBrickData.name}</span>
                        </div>
                        <div className="flex justify-between text-sm text-muted-foreground">
                          <span>{quantity.toLocaleString()} units</span>
                          <span>${selectedBrickData.price}/unit</span>
                        </div>
                      </div>
                      
                      <Separator />
                      
                      <div className="space-y-2">
                        <div className="flex justify-between">
                          <span>Subtotal</span>
                          <span>${subtotal.toFixed(2)}</span>
                        </div>
                        <div className="flex justify-between">
                          <span>Delivery Fee</span>
                          <span>{deliveryFee === 0 ? "FREE" : `$${deliveryFee.toFixed(2)}`}</span>
                        </div>
                        <div className="flex justify-between">
                          <span>Tax (15%)</span>
                          <span>${tax.toFixed(2)}</span>
                        </div>
                      </div>
                      
                      <Separator />
                      
                      <div className="flex justify-between font-bold text-lg">
                        <span>Total</span>
                        <span>${total.toFixed(2)}</span>
                      </div>
                      
                      <Button 
                        className="w-full" 
                        size="lg"
                        onClick={handleSubmitOrder}
                        disabled={!selectedBrick || !deliveryAddress || !deliveryDate}
                      >
                        Place Order
                      </Button>
                      
                      {deliveryFee === 0 && (
                        <p className="text-xs text-center text-muted-foreground">
                          🎉 Free delivery on orders over $1,000!
                        </p>
                      )}
                    </>
                  ) : (
                    <p className="text-muted-foreground text-center py-8">
                      Select a brick type to see order summary
                    </p>
                  )}
                </CardContent>
              </Card>
            </div>
          </div>
        </main>
      </div>
    </div>
  );
};

export default NewOrder;
