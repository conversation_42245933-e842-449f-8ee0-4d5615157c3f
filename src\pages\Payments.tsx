import { useState } from "react";
import { CreditCard, Download, Eye, Plus, Calendar, DollarSign, AlertCircle, CheckCircle } from "lucide-react";
import Header from "@/components/Layout/Header";
import Navigation from "@/components/Layout/Navigation";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Separator } from "@/components/ui/separator";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";

const mockInvoices = [
  {
    id: "INV-001",
    orderId: "ORD-001",
    amount: 2450.00,
    status: "paid",
    dueDate: "2024-01-20",
    paidDate: "2024-01-18",
    description: "Standard Red Brick - 10,000 units",
    paymentMethod: "Credit Card (**** 4532)"
  },
  {
    id: "INV-002",
    orderId: "ORD-002",
    amount: 1850.00,
    status: "paid",
    dueDate: "2024-01-25",
    paidDate: "2024-01-22",
    description: "Custom Clay Brick - 5,000 units",
    paymentMethod: "Bank Transfer"
  },
  {
    id: "INV-003",
    orderId: "ORD-003",
    amount: 3200.00,
    status: "pending",
    dueDate: "2024-01-30",
    paidDate: null,
    description: "Fire Brick - 2,500 units",
    paymentMethod: null
  },
  {
    id: "INV-004",
    orderId: "ORD-004",
    amount: 3675.00,
    status: "overdue",
    dueDate: "2024-01-15",
    paidDate: null,
    description: "Standard Red Brick - 15,000 units",
    paymentMethod: null
  }
];

const mockPaymentMethods = [
  {
    id: "card-1",
    type: "credit",
    last4: "4532",
    brand: "Visa",
    expiryMonth: "12",
    expiryYear: "2026",
    isDefault: true
  },
  {
    id: "card-2",
    type: "credit",
    last4: "8901",
    brand: "Mastercard",
    expiryMonth: "08",
    expiryYear: "2025",
    isDefault: false
  }
];

const Payments = () => {
  const [selectedInvoice, setSelectedInvoice] = useState<string | null>(null);
  const [showAddPaymentMethod, setShowAddPaymentMethod] = useState(false);

  const getStatusBadge = (status: string) => {
    const statusConfig = {
      paid: { color: "bg-green-100 text-green-800 border-green-200", icon: CheckCircle },
      pending: { color: "bg-yellow-100 text-yellow-800 border-yellow-200", icon: AlertCircle },
      overdue: { color: "bg-red-100 text-red-800 border-red-200", icon: AlertCircle }
    };

    const config = statusConfig[status as keyof typeof statusConfig];
    const Icon = config?.icon || AlertCircle;

    return (
      <Badge className={`${config?.color} border`}>
        <div className="flex items-center space-x-1">
          <Icon className="h-3 w-3" />
          <span className="capitalize">{status}</span>
        </div>
      </Badge>
    );
  };

  const totalPaid = mockInvoices.filter(inv => inv.status === "paid").reduce((sum, inv) => sum + inv.amount, 0);
  const totalPending = mockInvoices.filter(inv => inv.status === "pending").reduce((sum, inv) => sum + inv.amount, 0);
  const totalOverdue = mockInvoices.filter(inv => inv.status === "overdue").reduce((sum, inv) => sum + inv.amount, 0);

  const handlePayInvoice = (invoiceId: string) => {
    alert(`Payment initiated for invoice ${invoiceId}`);
  };

  return (
    <div className="min-h-screen bg-background">
      <Header />
      <div className="flex">
        <Navigation currentPath="/payments" />
        <main className="flex-1 p-6 space-y-6">
          <div className="flex flex-col space-y-2">
            <h1 className="text-3xl font-bold tracking-tight">Payments</h1>
            <p className="text-muted-foreground">
              Manage your invoices, payment methods, and transaction history.
            </p>
          </div>

          {/* Payment Summary */}
          <div className="grid gap-6 md:grid-cols-3">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium text-muted-foreground">Total Paid</CardTitle>
                <CheckCircle className="h-4 w-4 text-green-600" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-green-600">${totalPaid.toFixed(2)}</div>
                <p className="text-xs text-muted-foreground mt-1">This year</p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium text-muted-foreground">Pending</CardTitle>
                <AlertCircle className="h-4 w-4 text-yellow-600" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-yellow-600">${totalPending.toFixed(2)}</div>
                <p className="text-xs text-muted-foreground mt-1">Awaiting payment</p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium text-muted-foreground">Overdue</CardTitle>
                <AlertCircle className="h-4 w-4 text-red-600" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-red-600">${totalOverdue.toFixed(2)}</div>
                <p className="text-xs text-muted-foreground mt-1">Past due date</p>
              </CardContent>
            </Card>
          </div>

          <Tabs defaultValue="invoices" className="space-y-6">
            <TabsList>
              <TabsTrigger value="invoices">Invoices</TabsTrigger>
              <TabsTrigger value="payment-methods">Payment Methods</TabsTrigger>
              <TabsTrigger value="history">Payment History</TabsTrigger>
            </TabsList>

            <TabsContent value="invoices" className="space-y-4">
              {mockInvoices.map((invoice) => (
                <Card key={invoice.id} className="hover:shadow-md transition-shadow">
                  <CardContent className="pt-6">
                    <div className="flex items-center justify-between mb-4">
                      <div>
                        <h3 className="font-semibold text-lg">{invoice.id}</h3>
                        <p className="text-sm text-muted-foreground">Order: {invoice.orderId}</p>
                      </div>
                      {getStatusBadge(invoice.status)}
                    </div>

                    <div className="space-y-3">
                      <p className="text-sm">{invoice.description}</p>
                      
                      <div className="flex justify-between items-center">
                        <div className="flex items-center space-x-4 text-sm text-muted-foreground">
                          <div className="flex items-center space-x-1">
                            <Calendar className="h-4 w-4" />
                            <span>Due: {invoice.dueDate}</span>
                          </div>
                          {invoice.paidDate && (
                            <div className="flex items-center space-x-1">
                              <CheckCircle className="h-4 w-4 text-green-600" />
                              <span>Paid: {invoice.paidDate}</span>
                            </div>
                          )}
                        </div>
                        <span className="text-lg font-bold">${invoice.amount.toFixed(2)}</span>
                      </div>

                      <div className="flex space-x-2">
                        <Button variant="outline" size="sm">
                          <Eye className="h-4 w-4 mr-2" />
                          View
                        </Button>
                        <Button variant="outline" size="sm">
                          <Download className="h-4 w-4 mr-2" />
                          Download
                        </Button>
                        {invoice.status !== "paid" && (
                          <Button 
                            size="sm"
                            onClick={() => handlePayInvoice(invoice.id)}
                            className="ml-auto"
                          >
                            <CreditCard className="h-4 w-4 mr-2" />
                            Pay Now
                          </Button>
                        )}
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </TabsContent>

            <TabsContent value="payment-methods" className="space-y-4">
              <div className="flex justify-between items-center">
                <h3 className="text-lg font-semibold">Saved Payment Methods</h3>
                <Dialog open={showAddPaymentMethod} onOpenChange={setShowAddPaymentMethod}>
                  <DialogTrigger asChild>
                    <Button>
                      <Plus className="h-4 w-4 mr-2" />
                      Add Payment Method
                    </Button>
                  </DialogTrigger>
                  <DialogContent>
                    <DialogHeader>
                      <DialogTitle>Add Payment Method</DialogTitle>
                    </DialogHeader>
                    <div className="space-y-4">
                      <div className="space-y-2">
                        <Label htmlFor="card-number">Card Number</Label>
                        <Input id="card-number" placeholder="1234 5678 9012 3456" />
                      </div>
                      <div className="grid grid-cols-2 gap-4">
                        <div className="space-y-2">
                          <Label htmlFor="expiry">Expiry Date</Label>
                          <Input id="expiry" placeholder="MM/YY" />
                        </div>
                        <div className="space-y-2">
                          <Label htmlFor="cvv">CVV</Label>
                          <Input id="cvv" placeholder="123" />
                        </div>
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="name">Cardholder Name</Label>
                        <Input id="name" placeholder="John Doe" />
                      </div>
                      <Button className="w-full">Add Payment Method</Button>
                    </div>
                  </DialogContent>
                </Dialog>
              </div>

              {mockPaymentMethods.map((method) => (
                <Card key={method.id}>
                  <CardContent className="pt-6">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-4">
                        <div className="w-12 h-8 bg-gradient-to-r from-blue-600 to-purple-600 rounded flex items-center justify-center">
                          <CreditCard className="h-4 w-4 text-white" />
                        </div>
                        <div>
                          <p className="font-medium">{method.brand} •••• {method.last4}</p>
                          <p className="text-sm text-muted-foreground">
                            Expires {method.expiryMonth}/{method.expiryYear}
                          </p>
                        </div>
                      </div>
                      <div className="flex items-center space-x-2">
                        {method.isDefault && (
                          <Badge variant="secondary">Default</Badge>
                        )}
                        <Button variant="outline" size="sm">Edit</Button>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </TabsContent>

            <TabsContent value="history" className="space-y-4">
              <h3 className="text-lg font-semibold">Payment History</h3>
              {mockInvoices.filter(inv => inv.status === "paid").map((invoice) => (
                <Card key={invoice.id}>
                  <CardContent className="pt-6">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="font-medium">{invoice.id}</p>
                        <p className="text-sm text-muted-foreground">{invoice.description}</p>
                        <p className="text-xs text-muted-foreground mt-1">
                          Paid on {invoice.paidDate} via {invoice.paymentMethod}
                        </p>
                      </div>
                      <div className="text-right">
                        <p className="font-bold">${invoice.amount.toFixed(2)}</p>
                        <Badge className="bg-green-100 text-green-800 border-green-200 border">
                          <CheckCircle className="h-3 w-3 mr-1" />
                          Paid
                        </Badge>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </TabsContent>
          </Tabs>
        </main>
      </div>
    </div>
  );
};

export default Payments;
