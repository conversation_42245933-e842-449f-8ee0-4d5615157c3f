import { useState } from "react";
import { History, Search, Filter, Download, Eye, Calendar, Package, DollarSign } from "lucide-react";
import Header from "@/components/Layout/Header";
import Navigation from "@/components/Layout/Navigation";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { DatePickerWithRange } from "@/components/ui/date-range-picker";
import { Separator } from "@/components/ui/separator";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";

const mockOrderHistory = [
  {
    id: "ORD-001",
    type: "Standard Red Brick",
    quantity: "10,000 units",
    status: "delivered",
    orderDate: "2024-01-15",
    deliveryDate: "2024-01-20",
    amount: 2450.00,
    paymentStatus: "paid",
    deliveryAddress: "123 Construction Site, Building District, City",
    notes: "Delivered on time, excellent quality"
  },
  {
    id: "ORD-002",
    type: "Custom Clay Brick",
    quantity: "5,000 units",
    status: "delivered",
    orderDate: "2024-01-18",
    deliveryDate: "2024-01-25",
    amount: 1850.00,
    paymentStatus: "paid",
    deliveryAddress: "456 Residential Project, Suburb Area, City",
    notes: "Custom color matched perfectly"
  },
  {
    id: "ORD-003",
    type: "Fire Brick",
    quantity: "2,500 units",
    status: "processing",
    orderDate: "2024-01-20",
    deliveryDate: "2024-01-28",
    amount: 3200.00,
    paymentStatus: "pending",
    deliveryAddress: "789 Industrial Complex, Manufacturing Zone, City",
    notes: "Special heat-resistant specifications"
  },
  {
    id: "ORD-004",
    type: "Standard Red Brick",
    quantity: "15,000 units",
    status: "pending",
    orderDate: "2024-01-22",
    deliveryDate: "2024-01-30",
    amount: 3675.00,
    paymentStatus: "overdue",
    deliveryAddress: "321 Commercial Building, Downtown, City",
    notes: "Large order for commercial project"
  },
  {
    id: "ORD-005",
    type: "Engineering Brick",
    quantity: "8,000 units",
    status: "delivered",
    orderDate: "2023-12-10",
    deliveryDate: "2023-12-18",
    amount: 3600.00,
    paymentStatus: "paid",
    deliveryAddress: "555 Bridge Construction, Riverside, City",
    notes: "High-strength bricks for infrastructure"
  },
  {
    id: "ORD-006",
    type: "Custom Clay Brick",
    quantity: "3,000 units",
    status: "delivered",
    orderDate: "2023-11-25",
    deliveryDate: "2023-12-02",
    amount: 1050.00,
    paymentStatus: "paid",
    deliveryAddress: "888 Heritage Restoration, Historic District, City",
    notes: "Matched historical brick specifications"
  }
];

const OrderHistory = () => {
  const [searchTerm, setSearchTerm] = useState("");
  const [statusFilter, setStatusFilter] = useState("all");
  const [paymentFilter, setPaymentFilter] = useState("all");
  const [sortBy, setSortBy] = useState("date-desc");
  const [selectedOrder, setSelectedOrder] = useState<string | null>(null);

  const getStatusBadge = (status: string) => {
    const statusConfig = {
      pending: { color: "bg-yellow-100 text-yellow-800 border-yellow-200" },
      processing: { color: "bg-blue-100 text-blue-800 border-blue-200" },
      shipped: { color: "bg-purple-100 text-purple-800 border-purple-200" },
      delivered: { color: "bg-green-100 text-green-800 border-green-200" }
    };

    const config = statusConfig[status as keyof typeof statusConfig];
    return (
      <Badge className={`${config?.color} border`}>
        {status.charAt(0).toUpperCase() + status.slice(1)}
      </Badge>
    );
  };

  const getPaymentBadge = (status: string) => {
    const statusConfig = {
      paid: { color: "bg-green-100 text-green-800 border-green-200" },
      pending: { color: "bg-yellow-100 text-yellow-800 border-yellow-200" },
      overdue: { color: "bg-red-100 text-red-800 border-red-200" }
    };

    const config = statusConfig[status as keyof typeof statusConfig];
    return (
      <Badge className={`${config?.color} border`}>
        {status.charAt(0).toUpperCase() + status.slice(1)}
      </Badge>
    );
  };

  const filteredOrders = mockOrderHistory
    .filter(order => {
      const matchesSearch = order.id.toLowerCase().includes(searchTerm.toLowerCase()) ||
                           order.type.toLowerCase().includes(searchTerm.toLowerCase());
      const matchesStatus = statusFilter === "all" || order.status === statusFilter;
      const matchesPayment = paymentFilter === "all" || order.paymentStatus === paymentFilter;
      
      return matchesSearch && matchesStatus && matchesPayment;
    })
    .sort((a, b) => {
      switch (sortBy) {
        case "date-desc":
          return new Date(b.orderDate).getTime() - new Date(a.orderDate).getTime();
        case "date-asc":
          return new Date(a.orderDate).getTime() - new Date(b.orderDate).getTime();
        case "amount-desc":
          return b.amount - a.amount;
        case "amount-asc":
          return a.amount - b.amount;
        default:
          return 0;
      }
    });

  const selectedOrderData = selectedOrder ? mockOrderHistory.find(order => order.id === selectedOrder) : null;

  const totalOrders = mockOrderHistory.length;
  const totalSpent = mockOrderHistory.filter(order => order.paymentStatus === "paid").reduce((sum, order) => sum + order.amount, 0);
  const pendingPayments = mockOrderHistory.filter(order => order.paymentStatus !== "paid").reduce((sum, order) => sum + order.amount, 0);

  return (
    <div className="min-h-screen bg-background">
      <Header />
      <div className="flex">
        <Navigation currentPath="/history" />
        <main className="flex-1 p-6 space-y-6">
          <div className="flex flex-col space-y-2">
            <h1 className="text-3xl font-bold tracking-tight">Order History</h1>
            <p className="text-muted-foreground">
              View and manage all your past and current brick orders.
            </p>
          </div>

          {/* Summary Cards */}
          <div className="grid gap-6 md:grid-cols-3">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium text-muted-foreground">Total Orders</CardTitle>
                <Package className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{totalOrders}</div>
                <p className="text-xs text-muted-foreground mt-1">All time</p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium text-muted-foreground">Total Spent</CardTitle>
                <DollarSign className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">${totalSpent.toFixed(2)}</div>
                <p className="text-xs text-muted-foreground mt-1">Paid orders</p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium text-muted-foreground">Pending Payments</CardTitle>
                <Calendar className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">${pendingPayments.toFixed(2)}</div>
                <p className="text-xs text-muted-foreground mt-1">Outstanding amount</p>
              </CardContent>
            </Card>
          </div>

          {/* Filters and Search */}
          <Card>
            <CardContent className="pt-6">
              <div className="flex flex-col space-y-4 md:flex-row md:space-y-0 md:space-x-4">
                <div className="flex-1">
                  <div className="relative">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                    <Input
                      placeholder="Search orders..."
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                      className="pl-10"
                    />
                  </div>
                </div>
                
                <Select value={statusFilter} onValueChange={setStatusFilter}>
                  <SelectTrigger className="w-full md:w-[180px]">
                    <SelectValue placeholder="Order Status" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Statuses</SelectItem>
                    <SelectItem value="pending">Pending</SelectItem>
                    <SelectItem value="processing">Processing</SelectItem>
                    <SelectItem value="shipped">Shipped</SelectItem>
                    <SelectItem value="delivered">Delivered</SelectItem>
                  </SelectContent>
                </Select>

                <Select value={paymentFilter} onValueChange={setPaymentFilter}>
                  <SelectTrigger className="w-full md:w-[180px]">
                    <SelectValue placeholder="Payment Status" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Payments</SelectItem>
                    <SelectItem value="paid">Paid</SelectItem>
                    <SelectItem value="pending">Pending</SelectItem>
                    <SelectItem value="overdue">Overdue</SelectItem>
                  </SelectContent>
                </Select>

                <Select value={sortBy} onValueChange={setSortBy}>
                  <SelectTrigger className="w-full md:w-[180px]">
                    <SelectValue placeholder="Sort By" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="date-desc">Newest First</SelectItem>
                    <SelectItem value="date-asc">Oldest First</SelectItem>
                    <SelectItem value="amount-desc">Highest Amount</SelectItem>
                    <SelectItem value="amount-asc">Lowest Amount</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </CardContent>
          </Card>

          {/* Orders List */}
          <div className="space-y-4">
            {filteredOrders.map((order) => (
              <Card key={order.id} className="hover:shadow-md transition-shadow">
                <CardContent className="pt-6">
                  <div className="flex items-center justify-between mb-4">
                    <div>
                      <h3 className="font-semibold text-lg">{order.id}</h3>
                      <p className="text-sm text-muted-foreground">{order.type}</p>
                    </div>
                    <div className="flex space-x-2">
                      {getStatusBadge(order.status)}
                      {getPaymentBadge(order.paymentStatus)}
                    </div>
                  </div>

                  <div className="grid gap-4 md:grid-cols-4 mb-4">
                    <div>
                      <p className="text-sm text-muted-foreground">Quantity</p>
                      <p className="font-medium">{order.quantity}</p>
                    </div>
                    <div>
                      <p className="text-sm text-muted-foreground">Order Date</p>
                      <p className="font-medium">{order.orderDate}</p>
                    </div>
                    <div>
                      <p className="text-sm text-muted-foreground">Delivery Date</p>
                      <p className="font-medium">{order.deliveryDate}</p>
                    </div>
                    <div>
                      <p className="text-sm text-muted-foreground">Amount</p>
                      <p className="font-medium">${order.amount.toFixed(2)}</p>
                    </div>
                  </div>

                  <div className="flex space-x-2">
                    <Dialog>
                      <DialogTrigger asChild>
                        <Button variant="outline" size="sm" onClick={() => setSelectedOrder(order.id)}>
                          <Eye className="h-4 w-4 mr-2" />
                          View Details
                        </Button>
                      </DialogTrigger>
                      <DialogContent className="max-w-2xl">
                        <DialogHeader>
                          <DialogTitle>Order Details - {order.id}</DialogTitle>
                        </DialogHeader>
                        {selectedOrderData && (
                          <div className="space-y-4">
                            <div className="grid gap-4 md:grid-cols-2">
                              <div>
                                <h4 className="font-semibold mb-2">Order Information</h4>
                                <div className="space-y-2 text-sm">
                                  <div className="flex justify-between">
                                    <span>Product:</span>
                                    <span className="font-medium">{selectedOrderData.type}</span>
                                  </div>
                                  <div className="flex justify-between">
                                    <span>Quantity:</span>
                                    <span className="font-medium">{selectedOrderData.quantity}</span>
                                  </div>
                                  <div className="flex justify-between">
                                    <span>Amount:</span>
                                    <span className="font-medium">${selectedOrderData.amount.toFixed(2)}</span>
                                  </div>
                                </div>
                              </div>
                              <div>
                                <h4 className="font-semibold mb-2">Status</h4>
                                <div className="space-y-2">
                                  {getStatusBadge(selectedOrderData.status)}
                                  {getPaymentBadge(selectedOrderData.paymentStatus)}
                                </div>
                              </div>
                            </div>
                            
                            <Separator />
                            
                            <div>
                              <h4 className="font-semibold mb-2">Delivery Address</h4>
                              <p className="text-sm text-muted-foreground">{selectedOrderData.deliveryAddress}</p>
                            </div>
                            
                            <div>
                              <h4 className="font-semibold mb-2">Notes</h4>
                              <p className="text-sm text-muted-foreground">{selectedOrderData.notes}</p>
                            </div>
                          </div>
                        )}
                      </DialogContent>
                    </Dialog>
                    
                    <Button variant="outline" size="sm">
                      <Download className="h-4 w-4 mr-2" />
                      Invoice
                    </Button>
                    
                    {order.status === "delivered" && (
                      <Button variant="outline" size="sm">
                        Reorder
                      </Button>
                    )}
                  </div>
                </CardContent>
              </Card>
            ))}

            {filteredOrders.length === 0 && (
              <Card>
                <CardContent className="pt-6 text-center py-12">
                  <History className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                  <h3 className="font-semibold mb-2">No orders found</h3>
                  <p className="text-muted-foreground">
                    {searchTerm || statusFilter !== "all" || paymentFilter !== "all" 
                      ? "Try adjusting your filters" 
                      : "You don't have any orders yet"}
                  </p>
                </CardContent>
              </Card>
            )}
          </div>
        </main>
      </div>
    </div>
  );
};

export default OrderHistory;
