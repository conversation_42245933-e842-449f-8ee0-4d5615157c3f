import { useState } from "react";
import { MessageSquare, Phone, Mail, Clock, Plus, Search, ChevronDown, ChevronRight } from "lucide-react";
import Header from "@/components/Layout/Header";
import Navigation from "@/components/Layout/Navigation";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Ta<PERSON>, Ta<PERSON>Content, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from "@/components/ui/collapsible";

const mockTickets = [
  {
    id: "TKT-001",
    subject: "Delivery delay for Order ORD-003",
    status: "open",
    priority: "high",
    created: "2024-01-22",
    lastUpdate: "2024-01-23",
    category: "delivery",
    description: "My fire brick order was supposed to be delivered yesterday but hasn't arrived yet."
  },
  {
    id: "TKT-002",
    subject: "Question about custom brick specifications",
    status: "resolved",
    priority: "medium",
    created: "2024-01-20",
    lastUpdate: "2024-01-21",
    category: "product",
    description: "Need clarification on the dimensions and weight specifications for custom clay bricks."
  },
  {
    id: "TKT-003",
    subject: "Invoice payment issue",
    status: "in-progress",
    priority: "high",
    created: "2024-01-19",
    lastUpdate: "2024-01-22",
    category: "billing",
    description: "Having trouble processing payment for invoice INV-004. Credit card keeps getting declined."
  }
];

const faqData = [
  {
    category: "Orders",
    questions: [
      {
        question: "What is the minimum order quantity?",
        answer: "The minimum order quantity is 100 units for all brick types. This ensures cost-effective production and delivery."
      },
      {
        question: "How long does it take to process an order?",
        answer: "Standard orders typically take 3-5 business days to process. Custom orders may take 7-10 business days depending on specifications."
      },
      {
        question: "Can I modify my order after placing it?",
        answer: "Orders can be modified within 24 hours of placement. After production begins, modifications may not be possible."
      }
    ]
  },
  {
    category: "Delivery",
    questions: [
      {
        question: "What are your delivery areas?",
        answer: "We deliver within a 50-mile radius of our facility. For deliveries beyond this range, additional charges may apply."
      },
      {
        question: "Do you offer same-day delivery?",
        answer: "Same-day delivery is available for orders placed before 10 AM, subject to availability and location."
      },
      {
        question: "What if I'm not available during delivery?",
        answer: "We require someone to be present during delivery. If unavailable, we can reschedule or deliver to an authorized representative."
      }
    ]
  },
  {
    category: "Payment",
    questions: [
      {
        question: "What payment methods do you accept?",
        answer: "We accept credit cards (Visa, Mastercard, American Express), bank transfers, and company purchase orders for established customers."
      },
      {
        question: "When is payment due?",
        answer: "Payment is due within 30 days of invoice date. Early payment discounts may be available for large orders."
      }
    ]
  }
];

const Support = () => {
  const [selectedTicket, setSelectedTicket] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState("");
  const [expandedFAQ, setExpandedFAQ] = useState<string | null>(null);

  const getStatusBadge = (status: string) => {
    const statusConfig = {
      open: { color: "bg-blue-100 text-blue-800 border-blue-200" },
      "in-progress": { color: "bg-yellow-100 text-yellow-800 border-yellow-200" },
      resolved: { color: "bg-green-100 text-green-800 border-green-200" },
      closed: { color: "bg-gray-100 text-gray-800 border-gray-200" }
    };

    const config = statusConfig[status as keyof typeof statusConfig];
    return (
      <Badge className={`${config?.color} border`}>
        {status.replace("-", " ").toUpperCase()}
      </Badge>
    );
  };

  const getPriorityBadge = (priority: string) => {
    const priorityConfig = {
      low: { color: "bg-gray-100 text-gray-800 border-gray-200" },
      medium: { color: "bg-yellow-100 text-yellow-800 border-yellow-200" },
      high: { color: "bg-red-100 text-red-800 border-red-200" }
    };

    const config = priorityConfig[priority as keyof typeof priorityConfig];
    return (
      <Badge className={`${config?.color} border`}>
        {priority.toUpperCase()}
      </Badge>
    );
  };

  const filteredFAQ = faqData.map(category => ({
    ...category,
    questions: category.questions.filter(q => 
      q.question.toLowerCase().includes(searchTerm.toLowerCase()) ||
      q.answer.toLowerCase().includes(searchTerm.toLowerCase())
    )
  })).filter(category => category.questions.length > 0);

  const handleSubmitTicket = () => {
    alert("Support ticket submitted successfully! Ticket ID: TKT-" + Math.random().toString(36).substr(2, 3).toUpperCase());
  };

  return (
    <div className="min-h-screen bg-background">
      <Header />
      <div className="flex">
        <Navigation currentPath="/support" />
        <main className="flex-1 p-6 space-y-6">
          <div className="flex flex-col space-y-2">
            <h1 className="text-3xl font-bold tracking-tight">Support Center</h1>
            <p className="text-muted-foreground">
              Get help with your orders, find answers to common questions, or contact our support team.
            </p>
          </div>

          {/* Contact Options */}
          <div className="grid gap-4 md:grid-cols-3">
            <Card className="hover:shadow-md transition-shadow cursor-pointer">
              <CardContent className="pt-6 text-center">
                <Phone className="h-8 w-8 text-primary mx-auto mb-3" />
                <h3 className="font-semibold mb-2">Phone Support</h3>
                <p className="text-sm text-muted-foreground mb-3">
                  Speak with our support team
                </p>
                <p className="font-medium">+****************</p>
                <p className="text-xs text-muted-foreground">Mon-Fri 8AM-6PM</p>
              </CardContent>
            </Card>

            <Card className="hover:shadow-md transition-shadow cursor-pointer">
              <CardContent className="pt-6 text-center">
                <Mail className="h-8 w-8 text-primary mx-auto mb-3" />
                <h3 className="font-semibold mb-2">Email Support</h3>
                <p className="text-sm text-muted-foreground mb-3">
                  Send us a detailed message
                </p>
                <p className="font-medium"><EMAIL></p>
                <p className="text-xs text-muted-foreground">Response within 24hrs</p>
              </CardContent>
            </Card>

            <Card className="hover:shadow-md transition-shadow cursor-pointer">
              <CardContent className="pt-6 text-center">
                <Clock className="h-8 w-8 text-primary mx-auto mb-3" />
                <h3 className="font-semibold mb-2">Business Hours</h3>
                <p className="text-sm text-muted-foreground mb-3">
                  When we're available
                </p>
                <p className="font-medium">Mon-Fri: 8AM-6PM</p>
                <p className="text-xs text-muted-foreground">Sat: 9AM-2PM</p>
              </CardContent>
            </Card>
          </div>

          <Tabs defaultValue="tickets" className="space-y-6">
            <TabsList>
              <TabsTrigger value="tickets">Support Tickets</TabsTrigger>
              <TabsTrigger value="new-ticket">New Ticket</TabsTrigger>
              <TabsTrigger value="faq">FAQ</TabsTrigger>
            </TabsList>

            <TabsContent value="tickets" className="space-y-4">
              <div className="flex justify-between items-center">
                <h3 className="text-lg font-semibold">Your Support Tickets</h3>
                <Badge variant="secondary">{mockTickets.length} tickets</Badge>
              </div>

              {mockTickets.map((ticket) => (
                <Card key={ticket.id} className="hover:shadow-md transition-shadow">
                  <CardContent className="pt-6">
                    <div className="flex items-start justify-between mb-3">
                      <div>
                        <h4 className="font-semibold">{ticket.subject}</h4>
                        <p className="text-sm text-muted-foreground">{ticket.id}</p>
                      </div>
                      <div className="flex space-x-2">
                        {getStatusBadge(ticket.status)}
                        {getPriorityBadge(ticket.priority)}
                      </div>
                    </div>

                    <p className="text-sm text-muted-foreground mb-3">{ticket.description}</p>

                    <div className="flex justify-between items-center text-xs text-muted-foreground">
                      <span>Created: {ticket.created}</span>
                      <span>Last update: {ticket.lastUpdate}</span>
                    </div>

                    <div className="mt-3 pt-3 border-t">
                      <Button variant="outline" size="sm">
                        <MessageSquare className="h-4 w-4 mr-2" />
                        View Details
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </TabsContent>

            <TabsContent value="new-ticket" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle>Submit New Support Ticket</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid gap-4 md:grid-cols-2">
                    <div className="space-y-2">
                      <Label htmlFor="category">Category</Label>
                      <Select>
                        <SelectTrigger>
                          <SelectValue placeholder="Select category" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="order">Order Issues</SelectItem>
                          <SelectItem value="delivery">Delivery</SelectItem>
                          <SelectItem value="billing">Billing & Payment</SelectItem>
                          <SelectItem value="product">Product Information</SelectItem>
                          <SelectItem value="technical">Technical Support</SelectItem>
                          <SelectItem value="other">Other</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="priority">Priority</Label>
                      <Select>
                        <SelectTrigger>
                          <SelectValue placeholder="Select priority" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="low">Low</SelectItem>
                          <SelectItem value="medium">Medium</SelectItem>
                          <SelectItem value="high">High</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="subject">Subject</Label>
                    <Input id="subject" placeholder="Brief description of your issue" />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="order-id">Related Order ID (Optional)</Label>
                    <Input id="order-id" placeholder="e.g., ORD-001" />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="description">Description</Label>
                    <Textarea
                      id="description"
                      placeholder="Please provide detailed information about your issue..."
                      rows={5}
                    />
                  </div>

                  <Button onClick={handleSubmitTicket} className="w-full">
                    <Plus className="h-4 w-4 mr-2" />
                    Submit Ticket
                  </Button>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="faq" className="space-y-4">
              <div className="space-y-4">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                  <Input
                    placeholder="Search FAQ..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-10"
                  />
                </div>

                {filteredFAQ.map((category) => (
                  <Card key={category.category}>
                    <CardHeader>
                      <CardTitle className="text-lg">{category.category}</CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-2">
                      {category.questions.map((faq, index) => (
                        <Collapsible
                          key={index}
                          open={expandedFAQ === `${category.category}-${index}`}
                          onOpenChange={(open) => 
                            setExpandedFAQ(open ? `${category.category}-${index}` : null)
                          }
                        >
                          <CollapsibleTrigger className="flex items-center justify-between w-full p-3 text-left hover:bg-accent rounded-lg">
                            <span className="font-medium">{faq.question}</span>
                            {expandedFAQ === `${category.category}-${index}` ? (
                              <ChevronDown className="h-4 w-4" />
                            ) : (
                              <ChevronRight className="h-4 w-4" />
                            )}
                          </CollapsibleTrigger>
                          <CollapsibleContent className="px-3 pb-3">
                            <p className="text-muted-foreground">{faq.answer}</p>
                          </CollapsibleContent>
                        </Collapsible>
                      ))}
                    </CardContent>
                  </Card>
                ))}

                {filteredFAQ.length === 0 && searchTerm && (
                  <Card>
                    <CardContent className="pt-6 text-center py-12">
                      <Search className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                      <h3 className="font-semibold mb-2">No results found</h3>
                      <p className="text-muted-foreground">
                        Try different search terms or submit a support ticket for personalized help.
                      </p>
                    </CardContent>
                  </Card>
                )}
              </div>
            </TabsContent>
          </Tabs>
        </main>
      </div>
    </div>
  );
};

export default Support;
