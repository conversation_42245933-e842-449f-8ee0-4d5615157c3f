
import { useState } from "react";
import { Link } from "react-router-dom";
import {
  LayoutDashboard,
  Package,
  Truck,
  CreditCard,
  MessageSquare,
  History,
  Settings,
  BarChart3
} from "lucide-react";
import { cn } from "@/lib/utils";

interface NavItem {
  title: string;
  icon: any;
  href: string;
  badge?: number;
}

const customerNavItems: NavItem[] = [
  { title: "Dashboard", icon: LayoutDashboard, href: "/" },
  { title: "New Order", icon: Package, href: "/order" },
  { title: "Track Orders", icon: Truck, href: "/tracking" },
  { title: "Payments", icon: CreditCard, href: "/payments" },
  { title: "Support", icon: MessageSquare, href: "/support", badge: 2 },
  { title: "Order History", icon: History, href: "/history" },
  { title: "Settings", icon: Settings, href: "/settings" },
];

const adminNavItems: NavItem[] = [
  { title: "Admin Dashboard", icon: BarChart3, href: "/admin" },
  { title: "Order Management", icon: Package, href: "/admin/orders" },
  { title: "Customer Management", icon: MessageSquare, href: "/admin/customers" },
];

interface NavigationProps {
  userRole?: 'customer' | 'admin';
  currentPath?: string;
}

const Navigation = ({ userRole = 'customer', currentPath = '/' }: NavigationProps) => {
  const [collapsed, setCollapsed] = useState(false);
  const navItems = userRole === 'admin' ? adminNavItems : customerNavItems;

  return (
    <div className={cn(
      "flex flex-col border-r bg-card transition-all duration-300",
      collapsed ? "w-16" : "w-64"
    )}>
      <div className="flex-1 py-6">
        <nav className="space-y-2 px-3">
          {navItems.map((item) => {
            const Icon = item.icon;
            const isActive = currentPath === item.href;
            
            return (
              <Link
                key={item.href}
                to={item.href}
                className={cn(
                  "w-full flex items-center space-x-3 px-3 py-2.5 rounded-lg text-sm font-medium transition-colors",
                  isActive
                    ? "bg-primary text-primary-foreground shadow-sm"
                    : "text-muted-foreground hover:text-foreground hover:bg-accent"
                )}
              >
                <Icon className="h-5 w-5 flex-shrink-0" />
                {!collapsed && (
                  <>
                    <span className="flex-1 text-left">{item.title}</span>
                    {item.badge && (
                      <span className="bg-primary text-primary-foreground text-xs rounded-full px-2 py-0.5 min-w-[1.25rem] text-center">
                        {item.badge}
                      </span>
                    )}
                  </>
                )}
              </Link>
            );
          })}
        </nav>
      </div>
      
      <div className="border-t p-3">
        <button
          onClick={() => setCollapsed(!collapsed)}
          className="w-full flex items-center justify-center px-3 py-2 rounded-lg text-muted-foreground hover:text-foreground hover:bg-accent"
        >
          <Settings className="h-5 w-5" />
          {!collapsed && <span className="ml-3">Collapse</span>}
        </button>
      </div>
    </div>
  );
};

export default Navigation;
