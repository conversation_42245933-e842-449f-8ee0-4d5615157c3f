
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

/* Brick Manufacturing Portal Design System */
@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;

    /* Brand Colors - Earth Tones */
    --brick-red: 14 89% 53%;
    --brick-red-dark: 14 89% 43%;
    --clay-orange: 24 85% 58%;
    --earth-brown: 25 35% 35%;
    --warm-gray: 20 6% 45%;
    --construction-yellow: 48 96% 65%;
    
    /* UI Colors */
    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;
    --primary: 14 89% 53%;
    --primary-foreground: 210 40% 98%;
    --secondary: 20 6% 96%;
    --secondary-foreground: 25 35% 35%;
    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;
    --accent: 24 85% 58%;
    --accent-foreground: 210 40% 98%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;
    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 14 89% 53%;
    --radius: 0.75rem;

    /* Status Colors */
    --status-pending: 48 96% 65%;
    --status-processing: 24 85% 58%;
    --status-shipped: 142 76% 36%;
    --status-delivered: 142 69% 58%;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;
    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;
    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;
    --primary: 14 89% 53%;
    --primary-foreground: 222.2 47.4% 11.2%;
    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;
    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;
    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;
    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 212.7 26.8% 83.9%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  
  body {
    @apply bg-background text-foreground font-inter;
    font-feature-settings: "rlig" 1, "calt" 1;
  }
}

@layer components {
  .brick-pattern {
    background-image: 
      linear-gradient(90deg, hsl(var(--border)) 1px, transparent 1px),
      linear-gradient(hsl(var(--border)) 1px, transparent 1px);
    background-size: 60px 30px;
    background-position: 0 0, 30px 15px;
  }
  
  .status-badge {
    @apply inline-flex items-center rounded-full px-2.5 py-0.5 text-xs font-medium;
  }
  
  .status-pending {
    @apply bg-yellow-100 text-yellow-800;
    background-color: hsl(var(--status-pending) / 0.1);
    color: hsl(var(--status-pending));
  }
  
  .status-processing {
    @apply bg-orange-100 text-orange-800;
    background-color: hsl(var(--status-processing) / 0.1);
    color: hsl(var(--status-processing));
  }
  
  .status-shipped {
    @apply bg-blue-100 text-blue-800;
    background-color: hsl(142 76% 36% / 0.1);
    color: hsl(142 76% 36%);
  }
  
  .status-delivered {
    @apply bg-green-100 text-green-800;
    background-color: hsl(var(--status-delivered) / 0.1);
    color: hsl(var(--status-delivered));
  }
}


