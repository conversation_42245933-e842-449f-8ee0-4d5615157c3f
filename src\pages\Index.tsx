
import Header from "@/components/Layout/Header";
import Navigation from "@/components/Layout/Navigation";
import StatsCards from "@/components/Dashboard/StatsCards";
import RecentOrders from "@/components/Dashboard/RecentOrders";
import QuickActions from "@/components/Dashboard/QuickActions";

const Index = () => {
  return (
    <div className="min-h-screen bg-background">
      <Header />
      <div className="flex">
        <Navigation currentPath="/" />
        <main className="flex-1 p-6 space-y-6">
          <div className="flex flex-col space-y-2">
            <h1 className="text-3xl font-bold tracking-tight">Dashboard</h1>
            <p className="text-muted-foreground">
              Welcome back! Here's an overview of your brick orders and account.
            </p>
          </div>
          
          <StatsCards />
          
          <div className="grid gap-6 lg:grid-cols-3">
            <div className="lg:col-span-2">
              <RecentOrders />
            </div>
            <div>
              <QuickActions />
            </div>
          </div>
        </main>
      </div>
    </div>
  );
};

export default Index;
