
import { <PERSON>, MoreH<PERSON>zon<PERSON> } from "lucide-react";
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";

const orders = [
  {
    id: "ORD-001",
    type: "Standard Red Brick",
    quantity: "10,000 units",
    status: "delivered",
    date: "2024-01-15",
    amount: "$2,450"
  },
  {
    id: "ORD-002",
    type: "Custom Clay Brick",
    quantity: "5,000 units",
    status: "shipped",
    date: "2024-01-18",
    amount: "$1,850"
  },
  {
    id: "ORD-003",
    type: "Fire Brick",
    quantity: "2,500 units",
    status: "processing",
    date: "2024-01-20",
    amount: "$3,200"
  },
  {
    id: "ORD-004",
    type: "Standard Red Brick",
    quantity: "15,000 units",
    status: "pending",
    date: "2024-01-22",
    amount: "$3,675"
  }
];

const RecentOrders = () => {
  const getStatusBadge = (status: string) => {
    const statusClasses = {
      pending: "status-badge status-pending",
      processing: "status-badge status-processing", 
      shipped: "status-badge status-shipped",
      delivered: "status-badge status-delivered"
    };

    return (
      <Badge className={statusClasses[status as keyof typeof statusClasses] || "status-badge"}>
        {status.charAt(0).toUpperCase() + status.slice(1)}
      </Badge>
    );
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>Recent Orders</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {orders.map((order) => (
            <div key={order.id} className="flex items-center justify-between p-4 rounded-lg border bg-card hover:bg-accent/50 transition-colors">
              <div className="flex-1">
                <div className="flex items-center space-x-4">
                  <div className="flex-1">
                    <p className="font-medium">{order.id}</p>
                    <p className="text-sm text-muted-foreground">{order.type}</p>
                  </div>
                  <div className="text-right">
                    <p className="font-medium">{order.quantity}</p>
                    <p className="text-sm text-muted-foreground">{order.date}</p>
                  </div>
                  <div className="text-right">
                    <p className="font-medium">{order.amount}</p>
                    {getStatusBadge(order.status)}
                  </div>
                </div>
              </div>
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="ghost" className="h-8 w-8 p-0">
                    <MoreHorizontal className="h-4 w-4" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end">
                  <DropdownMenuItem>
                    <Eye className="mr-2 h-4 w-4" />
                    View Details
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  );
};

export default RecentOrders;
