import { useState } from "react";
import { Search, Package, Truck, CheckCircle, Clock, MapPin, Calendar, Eye } from "lucide-react";
import Header from "@/components/Layout/Header";
import Navigation from "@/components/Layout/Navigation";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { Separator } from "@/components/ui/separator";

const mockOrders = [
  {
    id: "ORD-001",
    type: "Standard Red Brick",
    quantity: "10,000 units",
    status: "delivered",
    progress: 100,
    orderDate: "2024-01-15",
    deliveryDate: "2024-01-20",
    estimatedDelivery: "2024-01-20",
    amount: "$2,450",
    trackingNumber: "TRK-001-2024",
    deliveryAddress: "123 Construction Site, Building District, City",
    timeline: [
      { status: "Order Placed", date: "2024-01-15", time: "09:30 AM", completed: true },
      { status: "Payment Confirmed", date: "2024-01-15", time: "10:15 AM", completed: true },
      { status: "In Production", date: "2024-01-16", time: "08:00 AM", completed: true },
      { status: "Quality Check", date: "2024-01-18", time: "02:30 PM", completed: true },
      { status: "Shipped", date: "2024-01-19", time: "07:45 AM", completed: true },
      { status: "Delivered", date: "2024-01-20", time: "11:20 AM", completed: true }
    ]
  },
  {
    id: "ORD-002",
    type: "Custom Clay Brick",
    quantity: "5,000 units",
    status: "shipped",
    progress: 85,
    orderDate: "2024-01-18",
    deliveryDate: "2024-01-25",
    estimatedDelivery: "2024-01-25",
    amount: "$1,850",
    trackingNumber: "TRK-002-2024",
    deliveryAddress: "456 Residential Project, Suburb Area, City",
    timeline: [
      { status: "Order Placed", date: "2024-01-18", time: "02:15 PM", completed: true },
      { status: "Payment Confirmed", date: "2024-01-18", time: "02:45 PM", completed: true },
      { status: "In Production", date: "2024-01-19", time: "08:00 AM", completed: true },
      { status: "Quality Check", date: "2024-01-22", time: "03:20 PM", completed: true },
      { status: "Shipped", date: "2024-01-24", time: "08:30 AM", completed: true },
      { status: "Out for Delivery", date: "2024-01-25", time: "06:00 AM", completed: false }
    ]
  },
  {
    id: "ORD-003",
    type: "Fire Brick",
    quantity: "2,500 units",
    status: "processing",
    progress: 45,
    orderDate: "2024-01-20",
    deliveryDate: "2024-01-28",
    estimatedDelivery: "2024-01-28",
    amount: "$3,200",
    trackingNumber: "TRK-003-2024",
    deliveryAddress: "789 Industrial Complex, Manufacturing Zone, City",
    timeline: [
      { status: "Order Placed", date: "2024-01-20", time: "11:00 AM", completed: true },
      { status: "Payment Confirmed", date: "2024-01-20", time: "11:30 AM", completed: true },
      { status: "In Production", date: "2024-01-22", time: "08:00 AM", completed: true },
      { status: "Quality Check", date: "2024-01-26", time: "TBD", completed: false },
      { status: "Shipped", date: "2024-01-27", time: "TBD", completed: false },
      { status: "Delivered", date: "2024-01-28", time: "TBD", completed: false }
    ]
  }
];

const TrackOrders = () => {
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedOrder, setSelectedOrder] = useState<string | null>(null);

  const filteredOrders = mockOrders.filter(order => 
    order.id.toLowerCase().includes(searchTerm.toLowerCase()) ||
    order.trackingNumber.toLowerCase().includes(searchTerm.toLowerCase()) ||
    order.type.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "pending": return <Clock className="h-4 w-4" />;
      case "processing": return <Package className="h-4 w-4" />;
      case "shipped": return <Truck className="h-4 w-4" />;
      case "delivered": return <CheckCircle className="h-4 w-4" />;
      default: return <Clock className="h-4 w-4" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case "pending": return "bg-yellow-100 text-yellow-800 border-yellow-200";
      case "processing": return "bg-blue-100 text-blue-800 border-blue-200";
      case "shipped": return "bg-purple-100 text-purple-800 border-purple-200";
      case "delivered": return "bg-green-100 text-green-800 border-green-200";
      default: return "bg-gray-100 text-gray-800 border-gray-200";
    }
  };

  const selectedOrderData = selectedOrder ? mockOrders.find(order => order.id === selectedOrder) : null;

  return (
    <div className="min-h-screen bg-background">
      <Header />
      <div className="flex">
        <Navigation currentPath="/tracking" />
        <main className="flex-1 p-6 space-y-6">
          <div className="flex flex-col space-y-2">
            <h1 className="text-3xl font-bold tracking-tight">Track Orders</h1>
            <p className="text-muted-foreground">
              Monitor the status and progress of your brick orders.
            </p>
          </div>

          {/* Search */}
          <Card>
            <CardContent className="pt-6">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Search by Order ID, Tracking Number, or Brick Type..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
            </CardContent>
          </Card>

          <div className="grid gap-6 lg:grid-cols-3">
            {/* Orders List */}
            <div className="lg:col-span-2 space-y-4">
              {filteredOrders.map((order) => (
                <Card key={order.id} className="hover:shadow-md transition-shadow">
                  <CardContent className="pt-6">
                    <div className="flex items-center justify-between mb-4">
                      <div>
                        <h3 className="font-semibold text-lg">{order.id}</h3>
                        <p className="text-sm text-muted-foreground">{order.trackingNumber}</p>
                      </div>
                      <Badge className={`${getStatusColor(order.status)} border`}>
                        <div className="flex items-center space-x-1">
                          {getStatusIcon(order.status)}
                          <span className="capitalize">{order.status}</span>
                        </div>
                      </Badge>
                    </div>

                    <div className="space-y-3">
                      <div className="flex justify-between items-center">
                        <span className="text-sm font-medium">{order.type}</span>
                        <span className="text-sm text-muted-foreground">{order.quantity}</span>
                      </div>

                      <div className="space-y-2">
                        <div className="flex justify-between text-sm">
                          <span>Progress</span>
                          <span>{order.progress}%</span>
                        </div>
                        <Progress value={order.progress} className="h-2" />
                      </div>

                      <div className="flex justify-between items-center text-sm">
                        <div className="flex items-center space-x-1">
                          <Calendar className="h-4 w-4 text-muted-foreground" />
                          <span>Est. Delivery: {order.estimatedDelivery}</span>
                        </div>
                        <span className="font-semibold">{order.amount}</span>
                      </div>

                      <Button
                        variant="outline"
                        size="sm"
                        className="w-full"
                        onClick={() => setSelectedOrder(order.id)}
                      >
                        <Eye className="h-4 w-4 mr-2" />
                        View Details
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              ))}

              {filteredOrders.length === 0 && (
                <Card>
                  <CardContent className="pt-6 text-center py-12">
                    <Package className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                    <h3 className="font-semibold mb-2">No orders found</h3>
                    <p className="text-muted-foreground">
                      {searchTerm ? "Try adjusting your search terms" : "You don't have any orders yet"}
                    </p>
                  </CardContent>
                </Card>
              )}
            </div>

            {/* Order Details */}
            <div>
              <Card className="sticky top-6">
                <CardHeader>
                  <CardTitle>Order Details</CardTitle>
                </CardHeader>
                <CardContent>
                  {selectedOrderData ? (
                    <div className="space-y-4">
                      <div>
                        <h3 className="font-semibold text-lg">{selectedOrderData.id}</h3>
                        <p className="text-sm text-muted-foreground">{selectedOrderData.trackingNumber}</p>
                      </div>

                      <Separator />

                      <div className="space-y-2">
                        <div className="flex justify-between">
                          <span className="text-sm">Product:</span>
                          <span className="text-sm font-medium">{selectedOrderData.type}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-sm">Quantity:</span>
                          <span className="text-sm font-medium">{selectedOrderData.quantity}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-sm">Amount:</span>
                          <span className="text-sm font-medium">{selectedOrderData.amount}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-sm">Order Date:</span>
                          <span className="text-sm font-medium">{selectedOrderData.orderDate}</span>
                        </div>
                      </div>

                      <Separator />

                      <div>
                        <div className="flex items-center space-x-2 mb-2">
                          <MapPin className="h-4 w-4 text-muted-foreground" />
                          <span className="text-sm font-medium">Delivery Address</span>
                        </div>
                        <p className="text-sm text-muted-foreground pl-6">
                          {selectedOrderData.deliveryAddress}
                        </p>
                      </div>

                      <Separator />

                      <div>
                        <h4 className="font-medium mb-3">Order Timeline</h4>
                        <div className="space-y-3">
                          {selectedOrderData.timeline.map((item, index) => (
                            <div key={index} className="flex items-start space-x-3">
                              <div className={`w-2 h-2 rounded-full mt-2 ${
                                item.completed ? "bg-green-500" : "bg-gray-300"
                              }`} />
                              <div className="flex-1">
                                <p className={`text-sm font-medium ${
                                  item.completed ? "text-foreground" : "text-muted-foreground"
                                }`}>
                                  {item.status}
                                </p>
                                <p className="text-xs text-muted-foreground">
                                  {item.date} {item.time !== "TBD" && `at ${item.time}`}
                                </p>
                              </div>
                            </div>
                          ))}
                        </div>
                      </div>
                    </div>
                  ) : (
                    <p className="text-muted-foreground text-center py-8">
                      Select an order to view details
                    </p>
                  )}
                </CardContent>
              </Card>
            </div>
          </div>
        </main>
      </div>
    </div>
  );
};

export default TrackOrders;
