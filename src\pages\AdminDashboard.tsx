import { BarChart3, Users, Package, DollarSign, TrendingUp, <PERSON><PERSON><PERSON><PERSON>gle, Clock, CheckCircle } from "lucide-react";
import Header from "@/components/Layout/Header";
import Navigation from "@/components/Layout/Navigation";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";

const adminStats = [
  {
    title: "Total Revenue",
    value: "$45,230",
    change: "+12.5%",
    changeType: "positive" as const,
    icon: DollarSign,
    description: "This month"
  },
  {
    title: "Active Orders",
    value: "127",
    change: "+8.2%",
    changeType: "positive" as const,
    icon: Package,
    description: "In progress"
  },
  {
    title: "Total Customers",
    value: "1,234",
    change: "+15.3%",
    changeType: "positive" as const,
    icon: Users,
    description: "Active accounts"
  },
  {
    title: "Production Capacity",
    value: "85%",
    change: "+5.1%",
    changeType: "positive" as const,
    icon: TrendingUp,
    description: "Current utilization"
  }
];

const recentOrders = [
  {
    id: "ORD-127",
    customer: "ABC Construction",
    type: "Standard Red Brick",
    quantity: "25,000 units",
    status: "processing",
    priority: "high",
    value: "$6,250",
    dueDate: "2024-01-30"
  },
  {
    id: "ORD-126",
    customer: "BuildRight Corp",
    type: "Fire Brick",
    quantity: "5,000 units",
    status: "shipped",
    priority: "medium",
    value: "$6,250",
    dueDate: "2024-01-28"
  },
  {
    id: "ORD-125",
    customer: "Metro Developers",
    type: "Custom Clay Brick",
    quantity: "15,000 units",
    status: "pending",
    priority: "low",
    value: "$5,250",
    dueDate: "2024-02-05"
  }
];

const productionMetrics = [
  { product: "Standard Red Brick", produced: 45000, target: 50000, percentage: 90 },
  { product: "Custom Clay Brick", produced: 12000, target: 15000, percentage: 80 },
  { product: "Fire Brick", produced: 8500, target: 10000, percentage: 85 },
  { product: "Engineering Brick", produced: 6800, target: 8000, percentage: 85 }
];

const alerts = [
  {
    id: 1,
    type: "warning",
    message: "Low inventory: Standard Red Brick raw materials",
    time: "2 hours ago"
  },
  {
    id: 2,
    type: "info",
    message: "New customer registration: XYZ Construction",
    time: "4 hours ago"
  },
  {
    id: 3,
    type: "error",
    message: "Production delay: Fire Brick batch #FB-2024-001",
    time: "6 hours ago"
  }
];

const AdminDashboard = () => {
  const getStatusBadge = (status: string) => {
    const statusConfig = {
      pending: { color: "bg-yellow-100 text-yellow-800 border-yellow-200", icon: Clock },
      processing: { color: "bg-blue-100 text-blue-800 border-blue-200", icon: Package },
      shipped: { color: "bg-purple-100 text-purple-800 border-purple-200", icon: TrendingUp },
      delivered: { color: "bg-green-100 text-green-800 border-green-200", icon: CheckCircle }
    };

    const config = statusConfig[status as keyof typeof statusConfig];
    const Icon = config?.icon || Clock;

    return (
      <Badge className={`${config?.color} border`}>
        <div className="flex items-center space-x-1">
          <Icon className="h-3 w-3" />
          <span className="capitalize">{status}</span>
        </div>
      </Badge>
    );
  };

  const getPriorityBadge = (priority: string) => {
    const priorityConfig = {
      low: { color: "bg-gray-100 text-gray-800 border-gray-200" },
      medium: { color: "bg-yellow-100 text-yellow-800 border-yellow-200" },
      high: { color: "bg-red-100 text-red-800 border-red-200" }
    };

    const config = priorityConfig[priority as keyof typeof priorityConfig];
    return (
      <Badge className={`${config?.color} border`}>
        {priority.toUpperCase()}
      </Badge>
    );
  };

  const getAlertIcon = (type: string) => {
    switch (type) {
      case "error": return <AlertTriangle className="h-4 w-4 text-red-500" />;
      case "warning": return <AlertTriangle className="h-4 w-4 text-yellow-500" />;
      default: return <AlertTriangle className="h-4 w-4 text-blue-500" />;
    }
  };

  return (
    <div className="min-h-screen bg-background">
      <Header />
      <div className="flex">
        <Navigation currentPath="/admin" userRole="admin" />
        <main className="flex-1 p-6 space-y-6">
          <div className="flex flex-col space-y-2">
            <h1 className="text-3xl font-bold tracking-tight">Admin Dashboard</h1>
            <p className="text-muted-foreground">
              Monitor business performance, manage operations, and oversee customer orders.
            </p>
          </div>

          {/* Stats Cards */}
          <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
            {adminStats.map((stat) => {
              const Icon = stat.icon;
              return (
                <Card key={stat.title} className="hover:shadow-md transition-shadow">
                  <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                    <CardTitle className="text-sm font-medium text-muted-foreground">
                      {stat.title}
                    </CardTitle>
                    <Icon className="h-4 w-4 text-muted-foreground" />
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">{stat.value}</div>
                    <p className="text-xs text-muted-foreground mt-1">
                      <span className="text-emerald-600 font-medium">{stat.change}</span> {stat.description}
                    </p>
                  </CardContent>
                </Card>
              );
            })}
          </div>

          <div className="grid gap-6 lg:grid-cols-3">
            {/* Recent Orders */}
            <div className="lg:col-span-2">
              <Card>
                <CardHeader>
                  <CardTitle>Recent Orders</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {recentOrders.map((order) => (
                      <div key={order.id} className="flex items-center justify-between p-4 rounded-lg border bg-card hover:bg-accent/50 transition-colors">
                        <div className="flex-1">
                          <div className="flex items-center space-x-4">
                            <div className="flex-1">
                              <p className="font-medium">{order.id}</p>
                              <p className="text-sm text-muted-foreground">{order.customer}</p>
                            </div>
                            <div className="text-right">
                              <p className="font-medium">{order.type}</p>
                              <p className="text-sm text-muted-foreground">{order.quantity}</p>
                            </div>
                            <div className="text-right">
                              <p className="font-medium">{order.value}</p>
                              <p className="text-sm text-muted-foreground">Due: {order.dueDate}</p>
                            </div>
                            <div className="flex flex-col space-y-1">
                              {getStatusBadge(order.status)}
                              {getPriorityBadge(order.priority)}
                            </div>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Alerts & Notifications */}
            <div className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle>System Alerts</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    {alerts.map((alert) => (
                      <div key={alert.id} className="flex items-start space-x-3 p-3 rounded-lg border">
                        {getAlertIcon(alert.type)}
                        <div className="flex-1">
                          <p className="text-sm font-medium">{alert.message}</p>
                          <p className="text-xs text-muted-foreground">{alert.time}</p>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Quick Actions</CardTitle>
                </CardHeader>
                <CardContent className="space-y-2">
                  <button className="w-full text-left p-3 rounded-lg hover:bg-accent transition-colors">
                    <p className="font-medium">View All Orders</p>
                    <p className="text-sm text-muted-foreground">Manage customer orders</p>
                  </button>
                  <button className="w-full text-left p-3 rounded-lg hover:bg-accent transition-colors">
                    <p className="font-medium">Customer Management</p>
                    <p className="text-sm text-muted-foreground">View customer accounts</p>
                  </button>
                  <button className="w-full text-left p-3 rounded-lg hover:bg-accent transition-colors">
                    <p className="font-medium">Production Report</p>
                    <p className="text-sm text-muted-foreground">Check production metrics</p>
                  </button>
                  <button className="w-full text-left p-3 rounded-lg hover:bg-accent transition-colors">
                    <p className="font-medium">Inventory Status</p>
                    <p className="text-sm text-muted-foreground">Monitor stock levels</p>
                  </button>
                </CardContent>
              </Card>
            </div>
          </div>

          {/* Production Metrics */}
          <Card>
            <CardHeader>
              <CardTitle>Production Metrics (This Month)</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
                {productionMetrics.map((metric) => (
                  <div key={metric.product} className="space-y-3">
                    <div className="flex justify-between items-center">
                      <h4 className="font-medium text-sm">{metric.product}</h4>
                      <span className="text-sm text-muted-foreground">{metric.percentage}%</span>
                    </div>
                    <Progress value={metric.percentage} className="h-2" />
                    <div className="flex justify-between text-xs text-muted-foreground">
                      <span>{metric.produced.toLocaleString()} produced</span>
                      <span>{metric.target.toLocaleString()} target</span>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </main>
      </div>
    </div>
  );
};

export default AdminDashboard;
